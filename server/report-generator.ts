import { FullReportData } from "@shared/schema";
import { Analysis } from "@shared/schema";
import { generateAndSavePDF, generatePDFBuffer } from "./pdf-generator";
import { makeOpenAIJSONRequest, makeOpenAITextRequest } from "./openai-utils";
import * as fs from 'fs';
import * as path from 'path';

// OpenAI utilities are now handled in openai-utils.ts

/**
 * Load and parse prompts from the external prompt file
 */
interface PromptTemplates {
  executiveSummary: string;
  detailedScores: string;
  techStack: string;
  feedbackStrategy: string;
  deploymentChecklist: string;
  testingRecommendations: string;
  documentationPlan: string;
  benchmarking: string;
  resourceLibrary: string;
  roadmap: string;
  visualizations: string;
  developerJourney: string;
}

let cachedPrompts: PromptTemplates | null = null;

/**
 * Load prompts from the external file and cache them
 */
function loadPromptTemplates(): PromptTemplates {
  if (cachedPrompts) {
    return cachedPrompts;
  }

  try {
    const promptFilePath = path.join(process.cwd(), 'prompts', 'prompt_sep012025.txt');
    const promptContent = fs.readFileSync(promptFilePath, 'utf8');

    // Parse the different sections from the prompt file
    const sections = promptContent.split('####');

    cachedPrompts = {
      executiveSummary: extractPromptFromSection(sections, '4.1') || getDefaultExecutiveSummaryPrompt(),
      detailedScores: extractPromptFromSection(sections, '4.2') || getDefaultDetailedScoresPrompt(),
      techStack: extractPromptFromSection(sections, '4.3') || getDefaultTechStackPrompt(),
      feedbackStrategy: extractPromptFromSection(sections, '4.4') || getDefaultFeedbackStrategyPrompt(),
      deploymentChecklist: extractPromptFromSection(sections, '4.5') || getDefaultDeploymentChecklistPrompt(),
      testingRecommendations: extractPromptFromSection(sections, '4.6') || getDefaultTestingRecommendationsPrompt(),
      documentationPlan: extractPromptFromSection(sections, '4.7') || getDefaultDocumentationPlanPrompt(),
      benchmarking: extractPromptFromSection(sections, '4.8') || getDefaultBenchmarkingPrompt(),
      resourceLibrary: extractPromptFromSection(sections, '4.9') || getDefaultResourceLibraryPrompt(),
      roadmap: extractPromptFromSection(sections, '4.10') || getDefaultRoadmapPrompt(),
      visualizations: extractPromptFromSection(sections, '4.11') || getDefaultVisualizationsPrompt(),
      developerJourney: extractPromptFromSection(sections, '4.12') || getDefaultDeveloperJourneyPrompt()
    };

    return cachedPrompts;
  } catch (error) {
    console.error('Error loading prompt templates:', error);
    // Return default prompts if file loading fails
    return getDefaultPromptTemplates();
  }
}

/**
 * Extract a specific prompt from a section of the prompt file
 */
function extractPromptFromSection(sections: string[], sectionNumber: string): string | null {
  const section = sections.find(s => s.trim().startsWith(sectionNumber));
  if (!section) return null;

  // Extract the prompt between the first ``` and the next ```
  const promptMatch = section.match(/```\s*\n([\s\S]*?)\n```/);
  return promptMatch ? promptMatch[1].trim() : null;
}

/**
 * Replace template variables in a prompt
 */
function replaceTemplateVariables(template: string, variables: Record<string, any>): string {
  let result = template;

  for (const [key, value] of Object.entries(variables)) {
    const placeholder = `{{${key}}}`;
    const replacement = Array.isArray(value) ? value.join(', ') : String(value);
    result = result.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), replacement);
  }

  return result;
}

/**
 * Default prompt templates (fallbacks)
 */
function getDefaultPromptTemplates(): PromptTemplates {
  return {
    executiveSummary: getDefaultExecutiveSummaryPrompt(),
    detailedScores: getDefaultDetailedScoresPrompt(),
    techStack: getDefaultTechStackPrompt(),
    feedbackStrategy: getDefaultFeedbackStrategyPrompt(),
    deploymentChecklist: getDefaultDeploymentChecklistPrompt(),
    testingRecommendations: getDefaultTestingRecommendationsPrompt(),
    documentationPlan: getDefaultDocumentationPlanPrompt(),
    benchmarking: getDefaultBenchmarkingPrompt(),
    resourceLibrary: getDefaultResourceLibraryPrompt(),
    roadmap: getDefaultRoadmapPrompt(),
    visualizations: getDefaultVisualizationsPrompt(),
    developerJourney: getDefaultDeveloperJourneyPrompt()
  };
}

function getDefaultExecutiveSummaryPrompt(): string {
  return `Given the project analysis data:
- Project Name: {{projectName}}
- Project Type: {{projectType}}
- Score: {{score}}/40
- MVP Readiness Level: {{readinessLevel}}
- Strengths: {{strengths}}
- Weaknesses: {{weaknesses}}
Generate an executive summary for a Vibe Coder. Include:
- The project name, type, and score.
- The MVP readiness level with a brief explanation.
- The top 3 strengths and top 3 areas for improvement, formatted as bullet points.
At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent (e.g., GitHub Copilot) to address the top areas for improvement overall, focusing on rapid iteration.
Keep the tone concise, professional, and encouraging for rapid iteration.`;
}

function getDefaultDetailedScoresPrompt(): string {
  return `Given the project analysis data for criterion {{criterion}}:
- Score: {{score}}/5
- Explanation: {{explanation}}
- Files Analyzed: {{files}}
- Code Issues: {{issues}}
- Technologies: {{technologies}}
Generate a detailed breakdown for a Vibe Coder. Include:
- The criterion name, score, and explanation.
- Specific examples from the project (e.g., file names, code snippets).
- A code snippet for improvement if applicable.
- Actionable suggestions for improvement.
At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to implement the suggestions for this criterion, referencing specific files and issues.
Keep the tone technical, actionable, and focused on rapid iteration.`;
}

function getDefaultTechStackPrompt(): string {
  return `Given the project analysis data:
- Technologies: {{technologies}}
- Performance Metrics: {{performance}}
- Dependencies: {{dependencies}}
Generate a technology stack analysis for a Vibe Coder. Include:
- A list of detected technologies.
- Pros and cons of the stack for rapid development.
- Recommendations for optimization.
- Alternative tools if applicable.
At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to apply the recommendations, such as optimizing dependencies or switching tools.
Keep the tone technical and focused on scalability and speed.`;
}

function getDefaultFeedbackStrategyPrompt(): string {
  return `Given the project analysis data for Feedback Collection:
- Score: {{score}}/5
- Gaps: {{gaps}}
- Technologies: {{technologies}}
Generate a user feedback strategy guide for a Vibe Coder. Include:
- The current score and identified gaps.
- A step-by-step guide to add a feedback system.
- A code snippet for implementation.
- Best practices for user feedback.
- Recommended tools.
At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to implement the step-by-step guide, including the code snippet.
Keep the tone practical and encouraging for rapid implementation.`;
}

function getDefaultDeploymentChecklistPrompt(): string {
  return `Given the project analysis data for Launch Readiness:
- Score: {{score}}/5
- Gaps: {{gaps}}
- Technologies: {{technologies}}
Generate a deployment and launch checklist for a Vibe Coder. Include:
- The current score and identified gaps.
- A checklist for launch preparation.
- A suggested timeline.
At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to set up deployment based on the checklist, referencing technologies.
Keep the tone actionable and focused on rapid deployment.`;
}

function getDefaultTestingRecommendationsPrompt(): string {
  return `Given the project analysis data for Testing:
- Score: {{score}}/5
- Gaps: {{gaps}}
- Files: {{files}}
Generate testing and stability recommendations for a Vibe Coder. Include:
- The current score and identified gaps.
- Specific testing gaps (e.g., missing tests for a file).
- Recommended frameworks.
- Sample test cases.
- A user testing plan.
At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to implement tests based on the recommendations and sample cases.
Keep the tone technical and actionable.`;
}

function getDefaultDocumentationPlanPrompt(): string {
  return `Given the project analysis data for Documentation:
- Score: {{score}}/5
- Gaps: {{gaps}}
- Files: {{files}}
Generate a documentation enhancement plan for a Vibe Coder. Include:
- The current score and identified gaps.
- Specific documentation gaps.
- A README template.
- Recommended tools.
- An example action.
At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to generate or update documentation based on the plan.
Keep the tone practical and focused on quick wins.`;
}

function getDefaultBenchmarkingPrompt(): string {
  return `Given the project analysis data:
- Score: {{score}}/40
- Criteria Scores: {{criteriaScores}}
- Industry Average: {{industryAverage}}
Generate a competitive benchmarking section for a Vibe Coder. Include:
- A comparison of the project's score to the industry average.
- Areas where the project excels.
- Areas to improve to match competitors.
At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to address areas to improve and align with industry averages.
Keep the tone motivational and data-driven.`;
}

function getDefaultResourceLibraryPrompt(): string {
  return `Given the project analysis data:
- Gaps: {{gaps}}
- Technologies: {{technologies}}
Generate a resource library for a Vibe Coder. Include:
- Tutorials relevant to identified gaps.
- Tools to address gaps.
- Communities for support.
- Templates for quick implementation.
At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to integrate resources or templates into their project.
Keep the tone helpful and resourceful.`;
}

function getDefaultRoadmapPrompt(): string {
  return `Given the project analysis data:
- Score: {{score}}/40
- Weaknesses: {{weaknesses}}
- Recommendations: {{recommendations}}
Generate a personalized roadmap for a Vibe Coder. Include:
- The current and target MVP readiness level.
- Actionable steps with priorities and estimated times.
- A suggested timeline.
At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to automate or assist with the roadmap steps.
Keep the tone actionable and encouraging.`;
}

function getDefaultVisualizationsPrompt(): string {
  return `Given the project analysis data:
- Scores: {{scores}}
- Code Metrics: {{codeMetrics}}
- Performance Metrics: {{performanceMetrics}}
- Industry Average: {{industryAverage}}
Generate visualizations and metrics for a Vibe Coder. Include:
- A radar chart of scores.
- Code quality metrics (e.g., coverage, complexity).
- Performance metrics (e.g., load time).
- A comparison chart vs. industry average.
At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to improve metrics like code coverage or performance.
Keep the tone data-driven and visual.`;
}

function getDefaultDeveloperJourneyPrompt(): string {
  return `Given the project analysis data:
- Project Name: {{projectName}}
- Score: {{score}}/40
- Readiness Level: {{readinessLevel}}
- Weaknesses: {{weaknesses}}
- Recommendations: {{recommendations}}
- Personalized Roadmap: {{roadmap}}
Generate a developer journey map to help finish the project for a Vibe Coder. Include:
- A step-by-step journey map in table format (e.g., Step, Description, Priority, Estimated Time, Related Sections).
- Key milestones tied to the roadmap (e.g., from Early MVP to Solid MVP).
- Tips for iteration and launch.
Keep the tone encouraging and structured for easy following.`;
}

/**
 * Generates a full detailed report based on analysis results
 */
export async function generateFullReport(analysis: Analysis): Promise<FullReportData> {
  // First, validate that we have the necessary data
  if (!analysis.scoreData || !analysis.projectInfo) {
    throw new Error("Cannot generate full report: Score data or project info missing");
  }

  try {
    // Create empty report structure
    const fullReport: FullReportData = {
      executiveSummary: '',
      detailedScores: [],
      techStack: {
        technologies: [],
        pros: [],
        cons: [],
        recommendations: [],
        alternatives: []
      },
      feedbackStrategy: {
        currentScore: 0,
        gaps: '',
        guide: '',
        codeSnippet: '',
        bestPractices: [],
        tools: []
      },
      deploymentChecklist: {
        currentScore: 0,
        gaps: '',
        hosting: '',
        performance: '',
        security: '',
        monitoring: '',
        timeline: ''
      },
      testingRecommendations: {
        currentScore: 0,
        gaps: [],
        frameworks: [],
        sampleTests: '',
        userTestingPlan: ''
      },
      documentationPlan: {
        currentScore: 0,
        gaps: [],
        readmeTemplate: '',
        tools: [],
        examples: ''
      },
      benchmarking: {
        score: 0,
        averageScore: 0,
        strengths: [],
        weaknesses: []
      },
      resourceLibrary: {
        tutorials: [],
        tools: [],
        communities: [],
        templates: []
      },
      roadmap: {
        currentLevel: '',
        targetLevel: '',
        steps: [],
        timeline: ''
      },
      visualizations: {
        radarChartData: null,
        codeQualityMetrics: {},
        // performanceMetrics: {}, // Commented out - can't measure performance from static code
        comparisonData: null
      }
    };

    // Get the scored data
    const scoreData = analysis.scoreData;
    const projectInfo = analysis.projectInfo;
    
    // Generate each section of the report in parallel
    const [
      executiveSummary,
      detailedScores,
      techStack,
      feedbackStrategy,
      deploymentChecklist,
      testingRecommendations,
      documentationPlan,
      benchmarking,
      resourceLibrary,
      roadmap,
      visualizations
    ] = await Promise.all([
      generateExecutiveSummary(projectInfo, scoreData),
      generateDetailedScores(projectInfo, scoreData),
      generateTechStackAnalysis(projectInfo, scoreData),
      generateFeedbackStrategy(projectInfo, scoreData),
      generateDeploymentChecklist(projectInfo, scoreData),
      generateTestingRecommendations(projectInfo, scoreData),
      generateDocumentationPlan(projectInfo, scoreData),
      generateBenchmarking(projectInfo, scoreData),
      generateResourceLibrary(projectInfo, scoreData),
      generateRoadmap(projectInfo, scoreData),
      generateVisualizations(projectInfo, scoreData)
    ]);

    // Combine all sections
    fullReport.executiveSummary = executiveSummary;
    fullReport.detailedScores = detailedScores;
    fullReport.techStack = techStack;
    fullReport.feedbackStrategy = feedbackStrategy;
    fullReport.deploymentChecklist = deploymentChecklist;
    fullReport.testingRecommendations = testingRecommendations;
    fullReport.documentationPlan = documentationPlan;
    fullReport.benchmarking = benchmarking;
    fullReport.resourceLibrary = resourceLibrary;
    fullReport.roadmap = roadmap;
    fullReport.visualizations = visualizations;

    return fullReport;
  } catch (error) {
    console.error("Error generating full report:", error);
    throw new Error("Failed to generate full report");
  }
}

/**
 * Generate executive summary section
 */
async function generateExecutiveSummary(projectInfo: any, scoreData: any): Promise<string> {
  try {
    const prompts = loadPromptTemplates();
    const promptTemplate = prompts.executiveSummary;

    const variables = {
      projectName: projectInfo.name,
      projectType: projectInfo.type || 'Web Application',
      score: scoreData.total,
      readinessLevel: scoreData.category,
      strengths: scoreData.strengths,
      weaknesses: scoreData.weaknesses
    };

    const prompt = replaceTemplateVariables(promptTemplate, variables);
    const result = await makeOpenAITextRequest(prompt);
    return result || `Executive Summary for ${projectInfo.name}\n\nScore: ${scoreData.total}/40\n\nThis project is in the ${scoreData.category} stage. Please see the detailed sections for more information.`;
  } catch (error) {
    console.error("Error generating executive summary:", error);
    return `Executive Summary for ${projectInfo.name}\n\nScore: ${scoreData.total}/40\n\nThis project is in the ${scoreData.category} stage. Please see the detailed sections for more information.`;
  }
}

/**
 * Generate detailed scores section
 */
async function generateDetailedScores(projectInfo: any, scoreData: any): Promise<Array<{
  name: string;
  score: number;
  explanation: string;
  examples: string;
  codeSnippet?: string;
  suggestions: string;
}>> {
  const criterionScores = scoreData.criterionScores;
  const detailedScores = [];

  for (const criterion of criterionScores) {
    try {
      const prompts = loadPromptTemplates();
      const promptTemplate = prompts.detailedScores;

      const variables = {
        criterion: criterion.name,
        score: criterion.score,
        explanation: criterion.feedback || criterion.description,
        files: projectInfo.files ? projectInfo.files.slice(0, 5) : ['Various project files'],
        issues: criterion.issues || 'See analysis details',
        technologies: projectInfo.technologies
      };

      const prompt = replaceTemplateVariables(promptTemplate, variables) + `

Format your response as JSON with these fields: explanation, examples, codeSnippet (optional), suggestions

Example format:
{
  "explanation": "There is no clear mechanism for collecting user feedback, which is crucial for iterating based on user needs.",
  "examples": "In index.js, there's no endpoint for user surveys or feedback forms. The project lacks any visible feedback integration.",
  "codeSnippet": "// Add to index.js\\napp.post('/feedback', (req, res) => {\\n  const feedback = req.body;\\n  // Save feedback to database (e.g., Firebase)\\n  res.status(200).send('Feedback received');\\n});",
  "suggestions": "1. Implement a simple feedback form using Formik in React to collect user input. 2. Integrate a tool like Google Analytics to track user behavior passively."
}`;

      const result = await makeOpenAIJSONRequest(prompt);
      
      detailedScores.push({
        name: criterion.name,
        score: criterion.score,
        explanation: result.explanation || "No explanation available",
        examples: result.examples || "No examples available",
        codeSnippet: result.codeSnippet || "",
        suggestions: String(result.suggestions || "No suggestions available")
      });
    } catch (error) {
      console.error(`Error generating detailed score for ${criterion.name}:`, error);
      detailedScores.push({
        name: criterion.name,
        score: criterion.score,
        explanation: String(criterion.feedback || "No feedback available"),
        examples: "Unable to generate specific examples",
        suggestions: "Please review the criterion description for improvement ideas"
      });
    }
  }

  return detailedScores;
}

/**
 * Generate tech stack analysis section
 */
async function generateTechStackAnalysis(projectInfo: any, scoreData: any): Promise<{
  technologies: string[];
  pros: string[];
  cons: string[];
  recommendations: string[];
  alternatives: string[];
}> {
  try {
    const prompts = loadPromptTemplates();
    const promptTemplate = prompts.techStack;

    const variables = {
      technologies: projectInfo.technologies,
      performance: scoreData.performanceMetrics || 'Load time analysis pending',
      dependencies: projectInfo.dependencies || 'Standard dependencies detected'
    };

    const prompt = replaceTemplateVariables(promptTemplate, variables) + `

Format your response as JSON with these fields: pros (array), cons (array), recommendations (array), alternatives (array)

Example format:
{
  "pros": [
    "Next.js enables fast iteration with server-side rendering",
    "Tailwind CSS speeds up UI development with utility classes",
    "TypeScript improves code reliability"
  ],
  "cons": [
    "Large Next.js bundles may impact performance if not optimized",
    "Tailwind CSS can lead to bloated CSS files if not purged properly"
  ],
  "recommendations": [
    "Lazy-load components in Next.js to improve load times",
    "Use next-purge-css to remove unused Tailwind styles"
  ],
  "alternatives": [
    "For smaller projects, consider PicoCSS for a lighter CSS framework"
  ]
}`;

    const result = await makeOpenAIJSONRequest(prompt);

    return {
      technologies: projectInfo.technologies,
      pros: result.pros,
      cons: result.cons,
      recommendations: result.recommendations,
      alternatives: result.alternatives
    };
  } catch (error) {
    console.error("Error generating tech stack analysis:", error);
    return {
      technologies: projectInfo.technologies,
      pros: ["Modern technologies", "Well-supported ecosystem"],
      cons: ["May have steep learning curve"],
      recommendations: ["Review documentation for best practices"],
      alternatives: ["Consider alternative frameworks if appropriate"]
    };
  }
}

/**
 * Generate feedback strategy section
 */
async function generateFeedbackStrategy(projectInfo: any, scoreData: any): Promise<{
  currentScore: number;
  gaps: string;
  guide: string;
  codeSnippet?: string;
  bestPractices: string[];
  tools: string[];
}> {
  // Find the feedback collection criterion
  const feedbackCriterion = scoreData.criterionScores.find((c: any) =>
    c.name.toLowerCase().includes('feedback'));
  
  const currentScore = feedbackCriterion ? feedbackCriterion.score : 0;
  
  try {
    const prompts = loadPromptTemplates();
    const promptTemplate = prompts.feedbackStrategy;

    const variables = {
      score: currentScore,
      gaps: feedbackCriterion ? feedbackCriterion.feedback : 'No visible feedback mechanism detected in the project',
      technologies: projectInfo.technologies
    };

    const prompt = replaceTemplateVariables(promptTemplate, variables) + `

Format your response as JSON with these fields: gaps (string), guide (string), codeSnippet (string), bestPractices (array), tools (array)

Example format:
{
  "gaps": "No visible feedback mechanism detected in the project.",
  "guide": "1. Create a feedback form component in React using React Hook Form. 2. Add a POST endpoint in index.js to handle form submissions. 3. Store responses in Firebase for analysis. 4. Display the form on a dedicated /feedback page.",
  "codeSnippet": "// FeedbackForm.js\\nimport { useForm } from 'react-hook-form';\\nexport default function FeedbackForm() {\\n  const { register, handleSubmit } = useForm();\\n  const onSubmit = async (data) => {\\n    await fetch('/feedback', { method: 'POST', body: JSON.stringify(data) });\\n  };\\n  return (\\n    <form onSubmit={handleSubmit(onSubmit)}>\\n      <input {...register('message')} placeholder=\\"Your feedback\\" />\\n      <button type=\\"submit\\">Submit</button>\\n    </form>\\n  );\\n}",
  "bestPractices": [
    "Keep surveys short (<5 questions) to maximize responses",
    "Offer incentives (e.g., early access) for feedback"
  ],
  "tools": [
    "Hotjar for heatmaps",
    "Google Analytics for user behavior tracking"
  ]
}`;

    const result = await makeOpenAIJSONRequest(prompt);
    
    return {
      currentScore,
      gaps: result.gaps,
      guide: result.guide,
      codeSnippet: result.codeSnippet,
      bestPractices: result.bestPractices,
      tools: result.tools
    };
  } catch (error) {
    console.error("Error generating feedback strategy:", error);
    return {
      currentScore,
      gaps: "Gaps in feedback collection mechanisms need to be addressed",
      guide: "Implement user surveys and feedback forms in your application",
      codeSnippet: "// Example feedback form component",
      bestPractices: ["Keep forms short", "Ask specific questions", "Follow up with users"],
      tools: ["Google Forms", "Typeform", "Hotjar", "UserTesting.com"]
    };
  }
}

/**
 * Generate deployment checklist section
 */
async function generateDeploymentChecklist(projectInfo: any, scoreData: any): Promise<{
  currentScore: number;
  gaps: string;
  hosting: string;
  performance: string;
  security: string;
  monitoring: string;
  timeline: string;
}> {
  // Find the launch readiness criterion
  const launchCriterion = scoreData.criterionScores.find((c: any) =>
    c.name.toLowerCase().includes('launch') || c.name.toLowerCase().includes('readiness'));
  
  const currentScore = launchCriterion ? launchCriterion.score : 0;
  
  try {
    const prompts = loadPromptTemplates();
    const promptTemplate = prompts.deploymentChecklist;

    const variables = {
      score: currentScore,
      gaps: launchCriterion ? launchCriterion.feedback : 'Ready for closed beta but not public release due to untested features',
      technologies: projectInfo.technologies
    };

    const prompt = replaceTemplateVariables(promptTemplate, variables) + `

Format your response as JSON with these fields: gaps (string), hosting (string), performance (string), security (string), monitoring (string), timeline (string)

Example format:
{
  "gaps": "Ready for closed beta but not public release due to untested features.",
  "hosting": "Deploy on Vercel for Next.js projects.",
  "performance": "Minify CSS/JS, enable compression.",
  "security": "Add helmet.js for HTTP headers.",
  "monitoring": "Integrate Sentry for error tracking.",
  "timeline": "Week 1: Fix bugs and deploy to Vercel. Week 2: Beta test with 10 users and gather feedback."
}`;

    const result = await makeOpenAIJSONRequest(prompt);
    
    return {
      currentScore,
      gaps: result.gaps,
      hosting: result.hosting,
      performance: result.performance,
      security: result.security,
      monitoring: result.monitoring,
      timeline: result.timeline
    };
  } catch (error) {
    console.error("Error generating deployment checklist:", error);
    return {
      currentScore,
      gaps: "Deployment readiness needs improvement",
      hosting: "Consider using a cloud platform suitable for your stack",
      performance: "Optimize assets and implement caching",
      security: "Implement proper authentication and data validation",
      monitoring: "Set up error tracking and performance monitoring",
      timeline: "2-3 weeks for preparation and deployment"
    };
  }
}

/**
 * Generate testing recommendations section
 */
async function generateTestingRecommendations(projectInfo: any, scoreData: any): Promise<{
  currentScore: number;
  gaps: string[];
  frameworks: string[];
  sampleTests: string;
  userTestingPlan: string;
}> {
  // Find the testing criterion
  const testingCriterion = scoreData.criterionScores.find((c: any) =>
    c.name.toLowerCase().includes('test'));
  
  const currentScore = testingCriterion ? testingCriterion.score : 0;
  
  try {
    const prompts = loadPromptTemplates();
    const promptTemplate = prompts.testingRecommendations;

    const variables = {
      score: currentScore,
      gaps: testingCriterion ? testingCriterion.feedback : 'No clear evidence of comprehensive testing strategy or user testing',
      files: projectInfo.files ? projectInfo.files.slice(0, 5) : ['Various project files']
    };

    const prompt = replaceTemplateVariables(promptTemplate, variables) + `

Format your response as JSON with these fields: gaps (array), frameworks (array), sampleTests (string), userTestingPlan (string)

Example format:
{
  "gaps": [
    "No unit tests for auth.js",
    "No end-to-end tests detected"
  ],
  "frameworks": [
    "Jest for unit tests",
    "Cypress for E2E tests"
  ],
  "sampleTests": "// auth.test.js\\ntest('user login', () => {\\n  const user = { email: '<EMAIL>', password: 'password' };\\n  expect(login(user)).toBe(true);\\n});",
  "userTestingPlan": "Recruit 5 beta testers via Discord. Ask them to test the login flow and provide feedback."
}`;

    const result = await makeOpenAIJSONRequest(prompt);

    return {
      currentScore,
      gaps: result.gaps,
      frameworks: result.frameworks,
      sampleTests: result.sampleTests,
      userTestingPlan: result.userTestingPlan
    };
  } catch (error) {
    console.error("Error generating testing recommendations:", error);
    return {
      currentScore,
      gaps: ["Lack of unit tests", "No end-to-end testing", "Missing edge case coverage"],
      frameworks: ["Jest", "React Testing Library", "Cypress"],
      sampleTests: "// Example test code\ntest('user login', () => {\n  expect(login(user)).toBeTruthy();\n});",
      userTestingPlan: "Recruit 5-10 test users and prepare specific testing scenarios"
    };
  }
}

/**
 * Generate documentation plan section
 */
async function generateDocumentationPlan(projectInfo: any, scoreData: any): Promise<{
  currentScore: number;
  gaps: string[];
  readmeTemplate: string;
  tools: string[];
  examples: string;
}> {
  // Find the documentation criterion
  const docsCriterion = scoreData.criterionScores.find((c: any) =>
    c.name.toLowerCase().includes('doc'));
  
  const currentScore = docsCriterion ? docsCriterion.score : 0;
  
  try {
    const prompts = loadPromptTemplates();
    const promptTemplate = prompts.documentationPlan;

    const variables = {
      score: currentScore,
      gaps: docsCriterion ? docsCriterion.feedback : 'Limited mention of documentation; no user guide',
      files: projectInfo.files ? projectInfo.files.slice(0, 5) : ['Various project files']
    };

    const prompt = replaceTemplateVariables(promptTemplate, variables) + `

Format your response as JSON with these fields: gaps (array), readmeTemplate (string), tools (array), examples (string)

Example format:
{
  "gaps": [
    "No user guide in the project",
    "README lacks setup instructions"
  ],
  "readmeTemplate": "# Project Name\\n\\n## Installation\\nSteps to install dependencies.\\n\\n## Usage\\nHow to run the project.\\n\\n## Contributing\\nGuidelines for contributors.",
  "tools": [
    "Use Docusaurus for a user-friendly docs site"
  ],
  "examples": "Add a docs/getting-started.md file with setup steps."
}`;

    const result = await makeOpenAIJSONRequest(prompt);

    return {
      currentScore,
      gaps: result.gaps,
      readmeTemplate: result.readmeTemplate,
      tools: result.tools,
      examples: result.examples
    };
  } catch (error) {
    console.error("Error generating documentation plan:", error);
    return {
      currentScore,
      gaps: ["Missing setup instructions", "No API documentation", "Lacks user guide"],
      readmeTemplate: "# Project Name\n\n## Description\n\n## Installation\n\n## Usage\n\n## API Documentation\n\n## Contributing\n\n## License",
      tools: ["Docusaurus", "GitHub Wiki", "JSDoc", "Swagger"],
      examples: "# API Endpoint\n\n`GET /api/users`\n\nReturns a list of users.\n\n## Parameters\n\n- `limit`: Maximum number of users to return"
    };
  }
}

/**
 * Generate benchmarking section
 */
async function generateBenchmarking(_projectInfo: any, scoreData: any): Promise<{
  score: number;
  averageScore: number;
  strengths: string[];
  weaknesses: string[];
}> {
  const totalScore = scoreData.total;
  // This would normally be fetched from a database of scores, but we'll simulate for now
  const averageScore = 27;
  
  try {
    const prompts = loadPromptTemplates();
    const promptTemplate = prompts.benchmarking;

    const variables = {
      score: totalScore,
      criteriaScores: scoreData.criterionScores.map((c: any) => `${c.name}: ${c.score}/5`),
      industryAverage: averageScore
    };

    const prompt = replaceTemplateVariables(promptTemplate, variables) + `

Format your response as JSON with these fields: strengths (array), weaknesses (array)

Example format:
{
  "strengths": [
    "UI consistency is better than 70% of peers"
  ],
  "weaknesses": [
    "Top MVPs have 80% test coverage; aim for 60% to start"
  ]
}`;

    const result = await makeOpenAIJSONRequest(prompt);

    return {
      score: totalScore,
      averageScore,
      strengths: result.strengths,
      weaknesses: result.weaknesses
    };
  } catch (error) {
    console.error("Error generating benchmarking:", error);
    return {
      score: totalScore,
      averageScore,
      strengths: scoreData.strengths.slice(0, 3),
      weaknesses: scoreData.weaknesses.slice(0, 3)
    };
  }
}

/**
 * Generate resource library section
 */
async function generateResourceLibrary(projectInfo: any, scoreData: any): Promise<{
  tutorials: string[];
  tools: string[];
  communities: string[];
  templates: string[];
}> {
  try {
    const prompts = loadPromptTemplates();
    const promptTemplate = prompts.resourceLibrary;

    const variables = {
      gaps: scoreData.weaknesses,
      technologies: projectInfo.technologies
    };

    const prompt = replaceTemplateVariables(promptTemplate, variables) + `

Format your response as JSON with these fields: tutorials (array), tools (array), communities (array), templates (array)

Example format:
{
  "tutorials": [
    "How to Add Feedback Forms in React (link)"
  ],
  "tools": [
    "Free testing tools: Jest, Cypress"
  ],
  "communities": [
    "Join the Vibe Coding Discord for peer feedback"
  ],
  "templates": [
    "Download a sample README template (link)"
  ]
}`;

    const result = await makeOpenAIJSONRequest(prompt);

    return {
      tutorials: result.tutorials,
      tools: result.tools,
      communities: result.communities,
      templates: result.templates
    };
  } catch (error) {
    console.error("Error generating resource library:", error);
    return {
      tutorials: ["Getting Started with React", "State Management Best Practices"],
      tools: ["React DevTools", "Lighthouse", "ESLint"],
      communities: ["Stack Overflow", "React Community Discord", "DEV.to"],
      templates: ["Create React App", "Next.js Starter Templates"]
    };
  }
}

/**
 * Generate roadmap section
 */
async function generateRoadmap(_projectInfo: any, scoreData: any): Promise<{
  currentLevel: string;
  targetLevel: string;
  steps: Array<{
    title: string;
    priority: 'High' | 'Medium' | 'Low';
    estimatedTime: string;
  }>;
  timeline: string;
}> {
  const currentScore = scoreData.total;
  const currentLevel = scoreData.category;
  let targetLevel = '';
  
  // Determine target level based on current score
  if (currentScore < 15) {
    targetLevel = "Basic MVP (15-25 points)";
  } else if (currentScore < 25) {
    targetLevel = "Solid MVP (26-35 points)";
  } else if (currentScore < 35) {
    targetLevel = "Polished MVP (36-40 points)";
  } else {
    targetLevel = "Production Ready";
  }
  
  try {
    const prompts = loadPromptTemplates();
    const promptTemplate = prompts.roadmap;

    const variables = {
      score: currentScore,
      weaknesses: scoreData.weaknesses,
      recommendations: scoreData.recommendations.map((r: any) => r.title)
    };

    const prompt = replaceTemplateVariables(promptTemplate, variables) + `

Format your response as JSON with these fields: steps (array of objects with title, priority, estimatedTime), timeline (string)

Example format:
{
  "steps": [
    {
      "title": "Add feedback form",
      "priority": "High",
      "estimatedTime": "2 hours"
    },
    {
      "title": "Write user guide",
      "priority": "Medium",
      "estimatedTime": "4 hours"
    },
    {
      "title": "Optimize images",
      "priority": "Low",
      "estimatedTime": "3 hours"
    }
  ],
  "timeline": "Reach Solid MVP in 2 weeks with 10 hours of work."
}`;

    const result = await makeOpenAIJSONRequest(prompt);

    return {
      currentLevel,
      targetLevel,
      steps: result.steps,
      timeline: result.timeline
    };
  } catch (error) {
    console.error("Error generating roadmap:", error);
    return {
      currentLevel,
      targetLevel,
      steps: scoreData.recommendations.map((r: any) => ({
        title: r.title,
        priority: r.priority,
        estimatedTime: "1-2 days"
      })),
      timeline: "2-4 weeks to reach target level with consistent effort"
    };
  }
}

/**
 * Generate visualizations section
 */
async function generateVisualizations(_projectInfo: any, scoreData: any): Promise<{
  radarChartData: any;
  codeQualityMetrics: Record<string, string | number>;
  performanceMetrics?: Record<string, string | number>; // Optional - can't measure from static code
  comparisonData: any;
}> {
  try {
    const prompts = loadPromptTemplates();
    const promptTemplate = prompts.visualizations;

    const variables = {
      scores: scoreData.criterionScores.map((c: any) => `${c.name}: ${c.score}/5`),
      codeMetrics: scoreData.codeMetrics || 'Standard metrics detected',
      performanceMetrics: scoreData.performanceMetrics || 'Load time analysis pending',
      industryAverage: 27
    };

    const prompt = replaceTemplateVariables(promptTemplate, variables) + `

Format your response as JSON with these fields: codeQualityMetrics (object)

Example format:
{
  "codeQualityMetrics": {
    "Code Coverage": "40%",
    "Cyclomatic Complexity": 5
  }
}`;

    const result = await makeOpenAIJSONRequest(prompt);

    // We already have radar chart data in the scoreData
    const radarChartData = scoreData.radarData;

    // Create comparison data
    const comparisonData = {
      labels: ["Your Score", "Average Score", "Top Score"],
      datasets: [{
        label: "MVP Readiness",
        data: [scoreData.total, 27, 38],
        backgroundColor: ["rgba(75, 192, 192, 0.6)", "rgba(54, 162, 235, 0.6)", "rgba(255, 159, 64, 0.6)"]
      }]
    };

    return {
      radarChartData,
      codeQualityMetrics: result.codeQualityMetrics || {
        "Code Coverage": "40%",
        "Cyclomatic Complexity": 5,
        "Maintainability Index": 65,
        "Technical Debt Ratio": "22%"
      },
      // performanceMetrics: result.performanceMetrics || {
      //   "Page Load Time": "3.2s",
      //   "First Contentful Paint": "1.8s",
      //   "Total Bundle Size": "1.2MB",
      //   "API Response Time": "450ms"
      // }, // Commented out - can't measure performance from static code
      comparisonData
    };
  } catch (error) {
    console.error("Error generating visualizations:", error);

    // Fallback data
    const radarChartData = scoreData.radarData;
    const codeQualityMetrics = {
      "Code Coverage": "40%",
      "Cyclomatic Complexity": 5,
      "Maintainability Index": 65,
      "Technical Debt Ratio": "22%"
    };
    // const performanceMetrics = {
    //   "Page Load Time": "3.2s",
    //   "First Contentful Paint": "1.8s",
    //   "Total Bundle Size": "1.2MB",
    //   "API Response Time": "450ms"
    // }; // Commented out - can't measure performance from static code
    const comparisonData = {
      labels: ["Your Score", "Average Score", "Top Score"],
      datasets: [{
        label: "MVP Readiness",
        data: [scoreData.total, 27, 38],
        backgroundColor: ["rgba(75, 192, 192, 0.6)", "rgba(54, 162, 235, 0.6)", "rgba(255, 159, 64, 0.6)"]
      }]
    };

    return {
      radarChartData,
      codeQualityMetrics,
      // performanceMetrics, // Commented out - can't measure performance from static code
      comparisonData
    };
  }
}

/**
 * Generate a full PDF report using the modular PDF generator
 */
export async function generatePDF(fullReport: FullReportData, analysisId: number, projectName: string = "Unknown Project"): Promise<Buffer | null> {
  return await generatePDFBuffer(fullReport, analysisId, projectName);
}

/**
 * Generate and save PDF, returning the download URL
 */
export async function generateAndSaveReportPDF(fullReport: FullReportData, analysisId: number, projectName: string = "Unknown Project"): Promise<string | null> {
  return await generateAndSavePDF(fullReport, analysisId, projectName);
}