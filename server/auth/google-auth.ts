import { Router } from 'express';
import { OAuth2Client } from 'google-auth-library';
import { storage } from '../db-storage';

const router = Router();
const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

// Verify Google token and create/login user
router.post('/google', async (req, res) => {
  try {
    const { credential } = req.body;
    
    if (!credential) {
      return res.status(400).json({ error: 'No credential provided' });
    }
    
    // Verify the Google token
    const ticket = await client.verifyIdToken({
      idToken: credential,
      audience: process.env.GOOGLE_CLIENT_ID,
    });
    
    const payload = ticket.getPayload();
    if (!payload) {
      return res.status(400).json({ error: 'Invalid token' });
    }

    const { sub: googleId, email, name, picture } = payload;

    if (!googleId || !email) {
      return res.status(400).json({ error: 'Missing required user information' });
    }

    // Check if user exists by Google ID
    let user = await storage.getUserByGoogleId(googleId);
    
    if (!user) {
      // Check by email
      user = await storage.getUserByEmail(email);
      if (user) {
        // Link Google account to existing user
        await storage.linkGoogleAccount(user.id, googleId);
      }
    }
    
    if (!user) {
      // Create new user
      user = await storage.createGoogleUser({
        googleId,
        email,
        name: name || '',
        avatarUrl: picture
      });
    }

    // Store user in session
    (req.session as any).userId = user.id;

    // Debug logging
    console.log('🔐 Session after login:', {
      sessionId: req.sessionID,
      userId: (req.session as any).userId,
      sessionData: req.session,
      cookies: req.headers.cookie
    });

    res.json({
      success: true,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        avatarUrl: user.avatarUrl
      }
    });
  } catch (error) {
    console.error('Google auth error:', error);
    res.status(500).json({ error: 'Authentication failed' });
  }
});

// Get current user
router.get('/me', async (req, res) => {
  try {
    const userId = (req.session as any)?.userId;

    // Debug logging
    console.log('🔍 Session check on /auth/me:', {
      sessionId: req.sessionID,
      userId: userId,
      sessionData: req.session,
      cookies: req.headers.cookie,
      hasSession: !!req.session,
      sessionKeys: Object.keys(req.session || {})
    });

    if (!userId) {
      console.log('❌ No userId found in session');
      return res.status(401).json({ error: 'Not authenticated' });
    }

    const user = await storage.getUserById(userId);
    
    if (!user) {
      // Clear invalid session
      req.session.destroy(() => {});
      return res.status(401).json({ error: 'User not found' });
    }

    res.json({ 
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        avatarUrl: user.avatarUrl
      }
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ error: 'Failed to get user' });
  }
});

// Logout
router.post('/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      console.error('Logout error:', err);
      return res.status(500).json({ error: 'Logout failed' });
    }
    res.json({ success: true });
  });
});

// Test endpoint to check session functionality
router.get('/test-session', (req, res) => {
  console.log('🧪 Testing session:', {
    sessionId: req.sessionID,
    session: req.session,
    cookies: req.headers.cookie
  });

  // Set a test value
  (req.session as any).testValue = Date.now();

  res.json({
    sessionId: req.sessionID,
    testValue: (req.session as any).testValue,
    hasSession: !!req.session
  });
});

export default router;
