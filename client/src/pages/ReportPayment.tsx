import { useState, useEffect } from "react";
import { usePara<PERSON>, useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

export default function ReportPayment() {
  const [, setLocation] = useLocation();
  const params = useParams<{ id: string }>();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [paymentDetails, setPaymentDetails] = useState({
    email: "",
    couponCode: ""
  });
  const [discountApplied, setDiscountApplied] = useState(false);
  const [totalPrice, setTotalPrice] = useState(9.99);

  // Get current user to auto-fill email
  const { data: user } = useQuery({
    queryKey: ['/auth/me'],
    retry: false
  });

  // Auto-fill email when user data is available
  useEffect(() => {
    if (user && (user as any)?.user?.email && !paymentDetails.email) {
      setPaymentDetails(prev => ({
        ...prev,
        email: (user as any).user.email
      }));
    }
  }, [user, paymentDetails.email]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPaymentDetails(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const applyCoupon = () => {
    // In a real implementation, this would validate the coupon with the server
    // For demo purposes, we'll just apply a simple discount
    if (paymentDetails.couponCode.toLowerCase() === "vibe20") {
      setDiscountApplied(true);
      setTotalPrice(7.99);
      toast({
        title: "Coupon Applied",
        description: "Discount of $2 has been applied to your order.",
        variant: "default"
      });
    } else {
      toast({
        title: "Invalid Coupon",
        description: "The coupon code you entered is invalid or expired.",
        variant: "destructive"
      });
    }
  };

  const processPayment = async () => {
    setLoading(true);
    try {
      // Create Stripe checkout session
      const response = await apiRequest(
        "POST",
        `/api/report/payment/${params.id}`,
        {
          email: paymentDetails.email || undefined
        }
      );

      const data = await response.json();
      if (response.ok && data.data?.checkoutUrl) {
        // Redirect to Stripe checkout
        window.location.href = data.data.checkoutUrl;
      } else {
        toast({
          title: "Payment Setup Failed",
          description: data.message || "There was an error setting up payment.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Payment error:", error);
      toast({
        title: "Payment Error",
        description: "There was an error setting up payment. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold text-center mb-8">
        Complete Your Payment
      </h1>

      <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
        {/* Main Payment Form */}
        <div className="md:col-span-3">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Payment Information</h2>
            <p className="text-sm text-gray-600 mb-6">
              Your payment information is encrypted and secure. We do not store your card details.
            </p>

            <div className="space-y-4">
              <div>
                <Label htmlFor="email" className="text-sm font-medium">Email (Optional)</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={paymentDetails.email}
                  onChange={handleInputChange}
                  className="mt-1"
                />
                <p className="text-xs text-gray-500 mt-1">
                  We'll send your receipt to this email address
                </p>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Secure Payment with Stripe</h4>
                <p className="text-sm text-blue-700">
                  Your payment will be processed securely through Stripe.
                  You'll be redirected to enter your card details on Stripe's secure checkout page.
                </p>
              </div>
              <div className="flex items-end space-x-2">
                <div className="flex-1">
                  <Label htmlFor="couponCode" className="text-sm font-medium">Coupon Code (Optional)</Label>
                  <Input
                    id="couponCode"
                    name="couponCode"
                    placeholder="VIBE20"
                    value={paymentDetails.couponCode}
                    onChange={handleInputChange}
                    className="mt-1"
                    disabled={discountApplied}
                  />
                </div>
                <Button 
                  variant="outline" 
                  onClick={applyCoupon} 
                  disabled={discountApplied || !paymentDetails.couponCode}
                >
                  Apply
                </Button>
              </div>
            </div>

            <Separator className="my-6" />

            <Button 
              className="w-full" 
              size="lg"
              onClick={processPayment}
              disabled={loading}
            >
              {loading ? "Processing..." : `Pay $${totalPrice.toFixed(2)}`}
            </Button>

            <p className="text-xs text-center text-gray-500 mt-4">
              By completing this purchase, you agree to our Terms of Service and Privacy Policy.
            </p>
          </Card>
        </div>

        {/* Order Summary */}
        <div className="md:col-span-2">
          <Card className="p-6 bg-gray-50">
            <h2 className="text-lg font-semibold mb-4">Order Summary</h2>
            
            <div className="space-y-3 mb-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Premium Report</span>
                <span className="font-medium">$9.99</span>
              </div>

              {discountApplied && (
                <div className="flex justify-between text-green-600">
                  <span>Discount (VIBE20)</span>
                  <span>-$2.00</span>
                </div>
              )}
            </div>

            <Separator className="my-4" />

            <div className="flex justify-between font-semibold text-lg mb-6">
              <span>Total</span>
              <span>${totalPrice.toFixed(2)}</span>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
              <h3 className="text-sm font-semibold text-blue-800 mb-2">What's Included:</h3>
              <ul className="space-y-2 text-sm text-blue-700">
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-500 mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>25+ page comprehensive report</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-500 mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>Code-level insights and examples</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-500 mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>Personalized roadmap with timeline</span>
                </li>
                <li className="flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-500 mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>PDF download for easy sharing</span>
                </li>
              </ul>
            </div>

            <div className="mt-6 text-center">
              <Button variant="link" onClick={() => setLocation(`/report/preview/${params.id}`)}>
                Back to Preview
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}